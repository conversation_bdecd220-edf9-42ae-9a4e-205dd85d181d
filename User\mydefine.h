#ifndef __MYDEFINE_H_
#define __MYDEFINE_H_

/*��׼C�⺯��*/
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <math.h>
#include <stdint.h>
#include <stdbool.h>

/*HAL��*/
#include "main.h"

/*APP*/
#include "usart_app.h"
#include "led_app.h"
#include "key_app.h"
#include "oled_app.h"
#include "motor_app.h"
#include "encoder_app.h"
#include "pid_app.h"
#include "jy901s_app.h"
#include "gray_app.h"
#include "my_timer.h"
#include "bno08x_app.h"

/*灰度传感器HAL驱动*/
#include "Module/grayscale_adc/grayscale_hal.h"
#include "Module/grayscale_adc/adc_hal.h"

/*������*/
#include "my_scheduler.h"

/*灰度传感器配置参数*/
#define GRAYSCALE_ADC_CHANNEL    ADC_CHANNEL_9  // PB1对应ADC12_IN9
#define GRAYSCALE_SAMPLE_COUNT   8              // 采样次数


#endif
