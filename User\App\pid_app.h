#ifndef __PID_APP_H__
#define __PID_APP_H__

#include "mydefine.h"
#include "pid.h"

// PID�����ṹ��
typedef struct
{
    float kp;          // ����ϵ��
    float ki;          // ����ϵ��
    float kd;          // ΢��ϵ��
    float out_min;     // �����Сֵ
    float out_max;     // ������ֵ
} PidParams_t;

void PID_Init(void);
void PID_Task(void);

extern bool pid_running;      // PID运行使能控制
extern int basic_speed;       // 基础速度

extern PID_T pid_speed_left;  // 左电机速度环
extern PID_T pid_speed_right; // 右电机速度环
extern PID_T pid_line;        // 循迹环

#endif
