#ifndef __GRAYSCALE_HAL_H__
#define __GRAYSCALE_HAL_H__

#include "stm32f4xx_hal.h"

// 传感器数据结构
typedef struct {
    uint16_t Analog_value[8];    // 原始模拟量值
    uint16_t Normal_value[8];    // 归一化后的值
    uint16_t Calibrated_white[8]; // 白校准基准值
    uint16_t Calibrated_black[8]; // 黑校准基准值
    uint16_t Gray_white[8];      // 白平衡灰度值
    uint16_t Gray_black[8];      // 黑平衡灰度值
    double Normal_factor[8];     // 归一化系数
    double bits;                 // ADC分辨率对应位数
    uint8_t Digital;             // 数字输出状态
    uint8_t Time_out;            // 超时标志
    uint8_t Tick;                // 时基计数器
    uint8_t ok;                  // 传感器就绪标志
} Grayscale_Sensor;

void Grayscale_HAL_Init(void); // 灰度传感器初始化
void Grayscale_Sensor_Init_First(Grayscale_Sensor* sensor); // 传感器结构体初始化
void Grayscale_HAL_ReadAll(Grayscale_Sensor* sensor); // 读取所有传感器数据
uint8_t Grayscale_HAL_GetDigital(Grayscale_Sensor* sensor); // 获取数字信号状态
uint16_t* Grayscale_HAL_GetAnalog(Grayscale_Sensor* sensor); // 获取原始模拟值

#endif
