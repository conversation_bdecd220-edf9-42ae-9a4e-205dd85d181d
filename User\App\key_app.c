#include "key_app.h"

extern UART_HandleTypeDef huart1;
extern uint8_t led_rgb[5];
extern bool pid_running;
extern uint8_t stop_flat;
/**
 * @brief 按键处理任务 - 简化寻迹控制
 */
void key_task(void)
{
	static uint8_t key_old = 0;

	uint8_t key_val = key_read();
	uint8_t key_down = key_val & (key_val ^ key_old);
	key_old = key_val;

	switch(key_down)
	{
		case 1: // Key1 - 启动寻迹
			pid_running = 1;
			stop_flat = 0;

			// LED指示：绿色表示运行
			led_rgb[0] = 0;  // 红色关闭
			led_rgb[1] = 1;  // 绿色开启
			led_rgb[2] = 0;  // 蓝色关闭

			my_printf(&huart1, "Line Following Started by Key1!\r\n");
		break;

		case 2: // Key2 - 停止寻迹
			pid_running = 0;
			stop_flat = 1;

			// 停止电机
			motor_break();

			// LED指示：红色表示停止
			led_rgb[0] = 1;  // 红色开启
			led_rgb[1] = 0;  // 绿色关闭
			led_rgb[2] = 0;  // 蓝色关闭

			my_printf(&huart1, "Line Following Stopped by Key2!\r\n");
		break;

		case 3: // Key3 - 重置系统
			pid_running = 0;
			stop_flat = 1;

			// 停止电机
			motor_break();

			// 重置PID控制器
			pid_reset(&pid_line);
			pid_reset(&pid_speed_left);
			pid_reset(&pid_speed_right);

			// LED指示：蓝色表示重置
			led_rgb[0] = 0;  // 红色关闭
			led_rgb[1] = 0;  // 绿色关闭
			led_rgb[2] = 1;  // 蓝色开启

			my_printf(&huart1, "System Reset by Key3!\r\n");
		break;

		case 4: // Key4 - 预留功能
			// LED指示：黄色表示预留功能
			led_rgb[0] = 1;  // 红色开启
			led_rgb[1] = 1;  // 绿色开启
			led_rgb[2] = 0;  // 蓝色关闭

			my_printf(&huart1, "Key4 - Reserved Function!\r\n");
		break;

		case 10: // User Key - 状态查询
			// 输出当前系统状态
			my_printf(&huart1, "System Status: PID=%s, Stop=%s\r\n",
					 pid_running ? "ON" : "OFF",
					 stop_flat ? "YES" : "NO");
		break;
	}
}
