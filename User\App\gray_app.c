#include "gray_app.h"
#include "../Module/grayscale_adc/grayscale_hal.h"

extern UART_HandleTypeDef huart1;

static Grayscale_Sensor gray_sensor; // 灰度传感器实例
unsigned char Digtal;

float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f}; 

float g_line_position_error; 

void Gray_Init(void)
{
    Grayscale_HAL_Init(); // 初始化HAL驱动
    Grayscale_Sensor_Init_First(&gray_sensor); // 初始化传感器结构体
    my_printf(&huart1, "Gray Sensor HAL Initialized Successfully!\r\n");
}

void Gray_Task(void)
{
    Grayscale_HAL_ReadAll(&gray_sensor); // 读取所有传感器数据
    uint8_t temp = Grayscale_HAL_GetDigital(&gray_sensor); // 获取数字状态
    if(temp == 0xAA)
    {
        return;
    }
    Digtal = temp; // 直接使用HAL返回的数字状态
		
//    my_printf(&huart1, "Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n",(Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
    float weighted_sum = 0;
    uint8_t black_line_count = 0;

    for(uint8_t i = 0; i < 8; i++)
    {
      if((Digtal>>i) & 0x01)
      {
        weighted_sum += gray_weights[i];
        black_line_count++;
      }
    }
    
    if(black_line_count > 0)
      g_line_position_error = weighted_sum / (float)black_line_count;
}
