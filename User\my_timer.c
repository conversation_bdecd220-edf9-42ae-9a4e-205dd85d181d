#include "my_timer.h"

extern int basic_speed;
extern uint8_t led_rgb[5];

// 简化定时器变量
unsigned char measure_timer5ms;      // 5ms任务计时器
unsigned int led_timer500ms;        // LED状态指示计时器

void timer_init(void)
{
  HAL_TIM_Base_Start_IT(&htim2);
}

/**
 * @brief TIM2定时器中断回调函数 - 1ms中断
 * @note 简化版本：只处理核心任务调度和LED状态
 */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  if(htim->Instance == htim2.Instance)
	{
		// 5ms任务调度
		if(++measure_timer5ms >= 5)
		{
			measure_timer5ms = 0;

			// 执行核心控制任务
			Encoder_Task();    // 编码器任务
			Gray_Task();       // 灰度传感器任务
			PID_Task();        // PID控制任务
		}

		// LED状态指示管理 - 500ms自动关闭
		if(led_rgb[0] == 1 && ++led_timer500ms >= 500)
		{
			led_rgb[0] = 0;
			led_timer500ms = 0;
		}
	}
}

// 移除复杂的状态机逻辑 - 简化寻迹系统不需要复杂的模式切换
