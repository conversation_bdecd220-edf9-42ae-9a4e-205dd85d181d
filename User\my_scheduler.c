#include "my_scheduler.h"

typedef struct{
	void(*task_fun)(void);
	uint32_t task_time;
	uint32_t last_time;
}task;

task all_task[]={
	{led_task,1,0},                    // LED状态指示任务 - 1ms
	{Gray_Task,5,0},                   // 灰度传感器任务 - 5ms
	{Encoder_Task,5,0},                // 编码器任务 - 5ms (电机反馈)
	{PID_Task,5,0},                    // PID控制任务 - 5ms
	{uart_task,5,0},                   // 串口通信任务 - 5ms
	{key_task,10,0},                   // 按键处理任务 - 10ms
	{oled_task,100,0},                 // OLED显示任务 - 100ms
};

uint8_t task_num;

void all_task_init(void)
{
	task_num = sizeof(all_task)/sizeof(task);

	// 显示和通信模块初始化
	my_oled_init();
	uart_init();

	// 硬件驱动初始化
	led_init();
	Motor_Init();            // 电机驱动初始化
	Encoder_Init();          // 编码器初始化(电机反馈)
	Gray_Init();             // 灰度传感器初始化

	// 控制算法初始化
	PID_Init();              // PID控制器初始化

	// 定时器初始化
	timer_init();
}

void all_task_run(void)
{
	for(uint8_t i=0;i<task_num;i++)
	{
		uint32_t now_time = HAL_GetTick();
		if(now_time >= all_task[i].last_time + all_task[i].task_time)
		{
			all_task[i].last_time = now_time;
			all_task[i].task_fun();
		}
	}	
}
