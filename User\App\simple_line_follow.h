#ifndef __SIMPLE_LINE_FOLLOW_H__
#define __SIMPLE_LINE_FOLLOW_H__

#include "mydefine.h"
#include "../Module/pid/pid.h"

// 寻迹系统状态枚举
typedef enum {
    LINE_FOLLOW_STOP = 0,    // 停止状态
    LINE_FOLLOW_RUNNING,     // 运行状态
    LINE_FOLLOW_ERROR        // 错误状态
} LineFollowState_t;

// 寻迹系统配置参数
typedef struct {
    float base_speed;        // 基础速度 (0-100)
    float kp;               // PID比例系数
    float ki;               // PID积分系数
    float kd;               // PID微分系数
    float output_limit;     // PID输出限幅
} LineFollowConfig_t;

// 寻迹系统状态信息
typedef struct {
    LineFollowState_t state;        // 当前状态
    float position_error;           // 位置误差
    float pid_output;              // PID输出
    uint8_t sensor_digital;        // 传感器数字状态
    float left_motor_speed;        // 左电机速度
    float right_motor_speed;       // 右电机速度
} LineFollowStatus_t;

// 函数声明
void Simple_LineFollow_Init(void);                              // 初始化寻迹系统
void Simple_LineFollow_Task(void);                              // 寻迹主任务
void Simple_LineFollow_Start(void);                             // 启动寻迹
void Simple_LineFollow_Stop(void);                              // 停止寻迹
void Simple_LineFollow_Reset(void);                             // 重置寻迹系统
LineFollowState_t Simple_LineFollow_GetState(void);             // 获取系统状态
LineFollowStatus_t* Simple_LineFollow_GetStatus(void);          // 获取状态信息
void Simple_LineFollow_SetConfig(LineFollowConfig_t* config);   // 设置配置参数
LineFollowConfig_t* Simple_LineFollow_GetConfig(void);          // 获取配置参数

// 外部变量声明
extern unsigned char Digtal;                    // 灰度传感器数字状态
extern float g_line_position_error;             // 线位置误差
extern float gray_weights[8];                   // 灰度权重数组

#endif // __SIMPLE_LINE_FOLLOW_H__
