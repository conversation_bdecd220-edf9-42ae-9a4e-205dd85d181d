#include "simple_line_follow.h"
#include "gray_app.h"
#include "motor_app.h"

extern UART_HandleTypeDef huart1;

// 寻迹系统私有变量
static PID_T line_pid;                          // 寻迹PID控制器
static LineFollowState_t current_state;         // 当前状态
static LineFollowStatus_t status_info;          // 状态信息
static LineFollowConfig_t config;               // 配置参数

// 默认配置参数
static const LineFollowConfig_t default_config = {
    .base_speed = 60.0f,        // 基础速度60%
    .kp = 8.25f,               // 比例系数
    .ki = 0.0f,                // 积分系数
    .kd = 0.0f,                // 微分系数
    .output_limit = 80.0f      // 输出限幅
};

/**
 * @brief 初始化寻迹系统
 */
void Simple_LineFollow_Init(void)
{
    // 初始化状态
    current_state = LINE_FOLLOW_STOP;
    
    // 加载默认配置
    config = default_config;
    
    // 初始化PID控制器
    pid_init(&line_pid, config.kp, config.ki, config.kd, 0.0f, config.output_limit);
    pid_set_target(&line_pid, 0.0f);  // 目标位置误差为0
    
    // 初始化状态信息
    status_info.state = current_state;
    status_info.position_error = 0.0f;
    status_info.pid_output = 0.0f;
    status_info.sensor_digital = 0;
    status_info.left_motor_speed = 0.0f;
    status_info.right_motor_speed = 0.0f;
    
    my_printf(&huart1, "Simple Line Follow System Initialized!\r\n");
}

/**
 * @brief 寻迹主任务 - 5ms周期调用
 */
void Simple_LineFollow_Task(void)
{
    // 只在运行状态下执行寻迹控制
    if(current_state != LINE_FOLLOW_RUNNING) {
        return;
    }
    
    // 更新状态信息
    status_info.position_error = g_line_position_error;
    status_info.sensor_digital = Digtal;
    
    // 检查传感器状态
    if(Digtal == 0x00) {
        // 没有检测到线，保持当前速度
        status_info.pid_output = 0.0f;
    } else {
        // 计算PID输出
        status_info.pid_output = pid_calculate_positional(&line_pid, g_line_position_error);
        
        // 限幅处理
        status_info.pid_output = pid_constrain(status_info.pid_output, 
                                             -config.output_limit, config.output_limit);
    }
    
    // 计算左右电机速度
    status_info.left_motor_speed = config.base_speed - status_info.pid_output;
    status_info.right_motor_speed = config.base_speed + status_info.pid_output;
    
    // 速度限幅 (0-100%)
    status_info.left_motor_speed = pid_constrain(status_info.left_motor_speed, 0.0f, 100.0f);
    status_info.right_motor_speed = pid_constrain(status_info.right_motor_speed, 0.0f, 100.0f);
    
    // 设置电机速度
    motor_set_l(status_info.left_motor_speed);
    motor_set_r(status_info.right_motor_speed);
}

/**
 * @brief 启动寻迹
 */
void Simple_LineFollow_Start(void)
{
    if(current_state == LINE_FOLLOW_STOP) {
        current_state = LINE_FOLLOW_RUNNING;
        status_info.state = current_state;
        
        // 重置PID控制器
        pid_reset(&line_pid);
        
        my_printf(&huart1, "Line Following Started!\r\n");
    }
}

/**
 * @brief 停止寻迹
 */
void Simple_LineFollow_Stop(void)
{
    if(current_state == LINE_FOLLOW_RUNNING) {
        current_state = LINE_FOLLOW_STOP;
        status_info.state = current_state;
        
        // 停止电机
        motor_break();
        
        // 清零状态信息
        status_info.left_motor_speed = 0.0f;
        status_info.right_motor_speed = 0.0f;
        status_info.pid_output = 0.0f;
        
        my_printf(&huart1, "Line Following Stopped!\r\n");
    }
}

/**
 * @brief 重置寻迹系统
 */
void Simple_LineFollow_Reset(void)
{
    // 先停止
    Simple_LineFollow_Stop();
    
    // 重置PID控制器
    pid_reset(&line_pid);
    
    // 重置状态信息
    status_info.position_error = 0.0f;
    status_info.pid_output = 0.0f;
    status_info.sensor_digital = 0;
    
    my_printf(&huart1, "Line Following System Reset!\r\n");
}

/**
 * @brief 获取系统状态
 */
LineFollowState_t Simple_LineFollow_GetState(void)
{
    return current_state;
}

/**
 * @brief 获取状态信息
 */
LineFollowStatus_t* Simple_LineFollow_GetStatus(void)
{
    return &status_info;
}

/**
 * @brief 设置配置参数
 */
void Simple_LineFollow_SetConfig(LineFollowConfig_t* new_config)
{
    if(new_config != NULL) {
        config = *new_config;
        
        // 更新PID参数
        pid_set_params(&line_pid, config.kp, config.ki, config.kd);
        pid_set_limit(&line_pid, config.output_limit);
        
        my_printf(&huart1, "Line Follow Config Updated: Speed=%.1f, Kp=%.2f\r\n", 
                 config.base_speed, config.kp);
    }
}

/**
 * @brief 获取配置参数
 */
LineFollowConfig_t* Simple_LineFollow_GetConfig(void)
{
    return &config;
}
