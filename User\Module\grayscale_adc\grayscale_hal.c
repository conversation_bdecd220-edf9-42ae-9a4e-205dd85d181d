#include "grayscale_hal.h"
#include "adc_hal.h"

// 地址线控制宏定义 - PC5(AD0)/PC4(AD1)/PD15(AD2)
#define Switch_Address_0(i) ((i)?(HAL_GPIO_WritePin(GPIOC, GPIO_PIN_5, GPIO_PIN_SET)) : (HAL_GPIO_WritePin(GPIOC, GPIO_PIN_5, GPIO_PIN_RESET)))
#define Switch_Address_1(i) ((i)?(HAL_GPIO_WritePin(GPIOC, GPIO_PIN_4, GPIO_PIN_SET)) : (HAL_GPIO_WritePin(GPIOC, GPIO_PIN_4, GPIO_PIN_RESET)))
#define Switch_Address_2(i) ((i)?(HAL_GPIO_WritePin(GPIOD, GPIO_PIN_15, GPIO_PIN_SET)) : (HAL_GPIO_WritePin(GPIOD, GPIO_PIN_15, GPIO_PIN_RESET)))

// 传感器配置
#define Direction 1 // 输出方向配置

// 灰度传感器初始化
void Grayscale_HAL_Init(void)
{
    __HAL_RCC_GPIOC_CLK_ENABLE(); // 使能GPIOC时钟
    __HAL_RCC_GPIOD_CLK_ENABLE(); // 使能GPIOD时钟
    
    // 配置地址线GPIO PC5,PC4,PD15为输出
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // PC5和PC4配置
    GPIO_InitStruct.Pin = GPIO_PIN_5 | GPIO_PIN_4;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP; // 推挽输出
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    
    // PD15配置
    GPIO_InitStruct.Pin = GPIO_PIN_15;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP; // 推挽输出
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
    
    // 初始化地址线为低电平
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_5 | GPIO_PIN_4, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_15, GPIO_PIN_RESET);
}

// 采集8个通道的模拟值并进行均值滤波
static void Get_Analog_value(uint16_t *result)
{
    uint8_t i,j;
    uint32_t Anolag=0;
    
    // 遍历8个传感器通道（3位地址线组合）
    for(i=0;i<8;i++)
    {
        // 通过地址线组合切换传感器通道（注意取反逻辑）
        Switch_Address_0(!(i&0x01));  // 地址线0，对应bit0
        Switch_Address_1(!(i&0x02));  // 地址线1，对应bit1
        Switch_Address_2(!(i&0x04));  // 地址线2，对应bit2

        // 每个通道采集8次ADC值进行均值滤波
        for(j=0;j<8;j++)
        {
            Anolag+=ADC_HAL_GetValue();  // 累加ADC采样值
        }
        if(!Direction)result[i]=Anolag/8;  // 计算平均值
        else result[7-i]=Anolag/8;  // 计算平均值
        Anolag=0;  // 重置累加器
    }
}

// 将模拟值转换为数字信号（二值化处理）
static void convertAnalogToDigital(uint16_t *adc_value, uint16_t *Gray_white, uint16_t *Gray_black, uint8_t *Digital)
{
    *Digital = 0; // 先清零
    for (int i = 0; i < 8; i++) {
        if (adc_value[i] < Gray_black[i]) {
            *Digital |= (1 << i);   // 低于黑阈值置1（检测到黑线）
        } else if (adc_value[i] > Gray_white[i]) {
            *Digital &= ~(1 << i);  // 超过白阈值置0（检测到白色）
        } else {
            // 中间灰度值：使用中值判断
            uint16_t mid_threshold = (Gray_white[i] + Gray_black[i]) / 2;
            if (adc_value[i] < mid_threshold) {
                *Digital |= (1 << i);   // 偏向黑色，置1
            } else {
                *Digital &= ~(1 << i);  // 偏向白色，置0
            }
        }
    }
}

// 归一化ADC值到指定范围
static void normalizeAnalogValues(uint16_t *adc_value, double *Normal_factor, uint16_t *Calibrated_black, uint16_t *result, double bits)
{
    for (int i = 0; i < 8; i++) {
        uint16_t n;
        // 计算归一化值（减去黑电平后缩放）
        if(adc_value[i]<Calibrated_black[i]) n=0;  // 低于黑电平归零
        else n = (adc_value[i] - Calibrated_black[i]) * Normal_factor[i];

        // 限幅处理
        if (n > bits) {
            n = bits;
        }
        result[i]=n;
    }
}

// 传感器结构体初始化（首次初始化）
void Grayscale_Sensor_Init_First(Grayscale_Sensor* sensor)
{
    // 设置固定的黑白阈值（参考原始代码）
    uint16_t white_thresholds[8] = {3197, 3117, 3193, 3146, 3154, 3177, 2907, 3171}; // 白色阈值
    uint16_t black_thresholds[8] = {100, 100, 100, 100, 100, 100, 100, 100};         // 黑色阈值

    for(int i = 0; i < 8; i++)
    {
        sensor->Analog_value[i] = 0;
        sensor->Normal_value[i] = 0;
        sensor->Calibrated_white[i] = white_thresholds[i];
        sensor->Calibrated_black[i] = black_thresholds[i];
        sensor->Gray_white[i] = white_thresholds[i];
        sensor->Gray_black[i] = black_thresholds[i];

        // 计算归一化系数
        if(white_thresholds[i] > black_thresholds[i]) {
            sensor->Normal_factor[i] = 4095.0 / (white_thresholds[i] - black_thresholds[i]);
        } else {
            sensor->Normal_factor[i] = 1.0;
        }
    }

    // 初始化状态变量
    sensor->Digital = 0;
    sensor->Time_out = 0;
    sensor->Tick = 0;
    sensor->ok = 1;  // 标记已完成校准
    sensor->bits = 4095.0; // 12位ADC
}

// 传感器主任务（无定时器版本）
void Grayscale_HAL_ReadAll(Grayscale_Sensor* sensor)
{
    Get_Analog_value(sensor->Analog_value);  // 采集数据
    convertAnalogToDigital(sensor->Analog_value, sensor->Gray_white, sensor->Gray_black, &sensor->Digital); // 二值化处理
    normalizeAnalogValues(sensor->Analog_value, sensor->Normal_factor, sensor->Calibrated_black, sensor->Normal_value, sensor->bits); // 归一化处理
}

// 获取数字信号状态
uint8_t Grayscale_HAL_GetDigital(Grayscale_Sensor* sensor)
{
    return sensor->Digital;  // 返回8位数字状态（每位对应一个传感器）
}

// 获取原始模拟值
uint16_t* Grayscale_HAL_GetAnalog(Grayscale_Sensor* sensor)
{
    return sensor->Analog_value;  // 返回模拟值数组指针
}
