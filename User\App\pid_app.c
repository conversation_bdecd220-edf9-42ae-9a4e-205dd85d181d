#include "pid_app.h"

#define V_L_MAX 120
#define V_R_MAX 120

extern UART_HandleTypeDef huart1;

extern Encoder left_encoder;
extern Encoder right_encoder;

int basic_speed = 80; // �����ٶ�

/* PID ������ʵ�� */
PID_T pid_speed_left;  // �����ٶȻ�
PID_T pid_speed_right; // �����ٶȻ�

PID_T pid_line;        // ѭ����

// 移除角度PID控制器 - 简化寻迹系统不需要角度控制

/* PID �������� */
//PidParams_t pid_params_left = {
//    .kp = 6.8f,     // ��ǿ�ı������ƣ��ӿ������ٶ�
//    .ki = 0.175f,     // ��߻����ٶȣ��ӿ쿿��Ŀ��
//    .kd = 0.5f,     // �ʵ�΢������ĩ�ڹ���
//    .out_min = -V_L_MAX,
//    .out_max = V_L_MAX,
//};

//PidParams_t pid_params_right = {
//    .kp = 7.0f,     // ��ǿ�ı������ƣ��ӿ������ٶ�
//    .ki = 0.2f,     // ��߻����ٶȣ��ӿ쿿��Ŀ��
//    .kd = 0.4f,     // �ʵ�΢������ĩ�ڹ���
//    .out_min = -V_R_MAX,
//    .out_max = V_R_MAX,
//};
PidParams_t pid_params_left = {
    .kp = 7.5f,     // ����һ�������Ӧ�����������ٶ�
    .ki = 0.22f,    // ������֣����ٸ�Ŀ������̬���
    .kd = 0.7f,     // ��ǿ΢�֣������и��ٶ��µ�С����
		.out_min = -V_L_MAX,
    .out_max = V_L_MAX,
	
};
PidParams_t pid_params_right = {
    .kp = 6.5f,     // ��΢�������������͸��ٶ�����С����
    .ki = 0.18f,    // �����ֱ������������һ����
    .kd = 0.6f,     // �Լ�ǿ΢�֣�������������΢����
	  .out_min = -V_R_MAX,
    .out_max = V_R_MAX,
};


PidParams_t pid_params_line = {
    .kp = 8.25f,
    .ki = 0.0f,
    .kd = 0.0f,
    .out_min = -80.0f,
    .out_max = 80.0f,
};


// 移除角度PID参数配置 - 简化寻迹系统不需要角度控制

void PID_Init(void)
{
  // 初始化左右电机速度PID控制器
  pid_init(&pid_speed_left,
           pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,
           0.0f, pid_params_left.out_max);

  pid_init(&pid_speed_right,
           pid_params_right.kp, pid_params_right.ki, pid_params_right.kd,
           0.0f, pid_params_right.out_max);

  // 初始化寻迹PID控制器
  pid_init(&pid_line,
           pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
           0.0f, pid_params_line.out_max);

  // 设置初始目标值
  pid_set_target(&pid_speed_left, basic_speed);
  pid_set_target(&pid_speed_right, basic_speed);
  pid_set_target(&pid_line, 0);  // 寻迹目标：位置误差为0
}

bool pid_running = false; // PID ����ʹ�ܿ���

unsigned char pid_control_mode = 0; // 0-�ǶȻ����ƣ�1-ѭ��������

void Line_PID_control(void) // ѭ��������
{
  int line_pid_output = 0;
  
  // ʹ��λ��ʽ PID ��������ѭ�����������
  line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);
  
  // ����޷�
  line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);
  
  // ����ֵ�������ٶȻ���Ŀ������
	pid_set_target(&pid_speed_left, basic_speed - line_pid_output);
	pid_set_target(&pid_speed_right, basic_speed + line_pid_output);
//	pid_set_target(&pid_speed_left, basic_speed);
//  pid_set_target(&pid_speed_right, basic_speed);
}

// 移除角度PID控制函数 - 简化寻迹系统不需要角度控制

uint8_t stop_flat = 0;
void PID_Task(void)
{
  if(pid_running == false) return;

    float output_left = 0, output_right = 0;

    // 简化控制逻辑：只使用寻迹控制模式
    basic_speed = 60;  // 基础速度60cm/s
    Line_PID_control(); // 执行寻迹PID控制
	
//		static uint16_t num = 0;
//		static uint8_t temp = 0;
//		if(++temp >= 50)
//		{
//			temp = 0;
//			num += 20;
//		}
//		
//		pid_set_target(&pid_speed_left, num);
//    pid_set_target(&pid_speed_right, num);

//		Line_PID_control();
	
		if(stop_flat == 1)
		{
			 pid_set_target(&pid_speed_left, 0);
			 pid_set_target(&pid_speed_right, 0);
		}
		
    // ʹ��λ��ʽ PID ���������ٶȻ��������
    output_left = pid_calculate_positional(&pid_speed_left, left_encoder.speed_cm_s);
    output_right = pid_calculate_positional(&pid_speed_right, right_encoder.speed_cm_s);
  
    // ����޷�
    output_left = pid_constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
    output_right = pid_constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);
    
    // ���õ���ٶ�
		float duty_l = output_left  / V_L_MAX * 100.0f;
		float duty_r = output_right / V_R_MAX * 100.0f;
		
		motor_set_l(duty_l);
    motor_set_r(duty_r);
		
//		my_printf(&huart1, "%f,%f\r\n", pid_line.target, g_line_position_error);
//		my_printf(&huart1, "%f,%f,%f\r\n", pid_speed_left.target,right_encoder.speed_cm_s, left_encoder.speed_cm_s);
//		my_printf(&huart1, "%f,%f\r\n", pid_speed_right.target, right_encoder.speed_cm_s);
}


